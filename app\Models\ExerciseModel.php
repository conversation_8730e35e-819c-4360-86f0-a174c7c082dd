<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table         = 'exercises';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_name',
        'gazzetted_no',
        'gazzetted_date',
        'advertisement_no',
        'advertisement_date',
        'mode_of_advertisement',
        'publish_date_from',
        'publish_date_to',
        'description',
        'pre_screen_criteria',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    // Validation - Updated to match actual database schema
    protected $validationRules = [
        'org_id'             => 'permit_empty|numeric',
        'exercise_name'      => 'permit_empty|max_length[255]',
        'gazzetted_no'       => 'permit_empty|max_length[255]',
        'gazzetted_date'     => 'permit_empty|valid_date',
        'advertisement_no'   => 'permit_empty|max_length[255]',
        'advertisement_date' => 'permit_empty|valid_date',
        'mode_of_advertisement' => 'permit_empty|max_length[100]',
        'publish_date_from'  => 'permit_empty|valid_date',
        'publish_date_to'    => 'permit_empty|valid_date',
        'status'             => 'permit_empty|in_list[published,draft,selection,archived]',
        'created_by'         => 'permit_empty|numeric',
        'updated_by'         => 'permit_empty|numeric'
    ];

    // Separate validation rules for creation (stricter)
    protected $insertValidationRules = [
        'org_id'             => 'required|numeric',
        'exercise_name'      => 'required|max_length[255]',
        'gazzetted_no'       => 'required|max_length[255]',
        'gazzetted_date'     => 'required|valid_date',
        'advertisement_no'   => 'required|max_length[255]',
        'advertisement_date' => 'required|valid_date',
        'mode_of_advertisement' => 'required|max_length[100]',
        'publish_date_from'  => 'required|valid_date',
        'publish_date_to'    => 'required|valid_date',
        'status'             => 'required|in_list[published,draft,selection,archived]',
        'created_by'         => 'required|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_name' => [
            'required'   => 'Exercise name is required',
            'max_length' => 'Exercise name cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Override insert method to use stricter validation rules
     */
    public function insert($data = null, bool $returnID = true)
    {
        // Temporarily set stricter validation rules for insert
        $originalRules = $this->validationRules;
        $this->validationRules = $this->insertValidationRules;

        try {
            $result = parent::insert($data, $returnID);
            // Restore original rules
            $this->validationRules = $originalRules;
            return $result;
        } catch (\Exception $e) {
            // Restore original rules even if exception occurs
            $this->validationRules = $originalRules;
            throw $e;
        }
    }

    /**
     * Get exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getExercisesByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }

    /**
     * Get active exercises (published status)
     *
     * @return array
     */
    public function getActiveExercises()
    {
        return $this->where('status', 'publish')
                    ->where('publish_date_from <=', date('Y-m-d'))
                    ->where('publish_date_to >=', date('Y-m-d'))
                    ->findAll();
    }

    /**
     * Get draft exercises
     *
     * @param int $orgId
     * @return array
     */
    public function getDraftExercises($orgId = null)
    {
        $builder = $this->where('status', 'draft');

        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }

        return $builder->findAll();
    }
}