<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use App\Models\ExerciseModel;

class ExercisesController extends ResourceController
{
    protected $helpers = ['form', 'url', 'info'];
    protected $exerciseModel;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
    }

    /**
     * Debug method to test database connectivity and model functionality
     */
    public function debug($id = 1)
    {
        try {
            echo "<h3>Database Debug Information</h3>";

            // Test database connection
            $db = \Config\Database::connect();
            echo "<p><strong>Database connected:</strong> " . ($db->connID ? 'YES' : 'NO') . "</p>";

            // Test if table exists
            $tableExists = $db->tableExists('exercises');
            echo "<p><strong>Table 'exercises' exists:</strong> " . ($tableExists ? 'YES' : 'NO') . "</p>";

            if ($tableExists) {
                // Get table structure
                $fields = $db->getFieldData('exercises');
                echo "<p><strong>Table fields:</strong></p><ul>";
                foreach ($fields as $field) {
                    echo "<li>{$field->name} ({$field->type})</li>";
                }
                echo "</ul>";

                // Test finding a record
                $exercise = $this->exerciseModel->find($id);
                echo "<p><strong>Exercise ID {$id} found:</strong> " . ($exercise ? 'YES' : 'NO') . "</p>";

                if ($exercise) {
                    echo "<p><strong>Exercise data:</strong></p>";
                    echo "<pre>" . json_encode($exercise, JSON_PRETTY_PRINT) . "</pre>";

                    // Test update functionality
                    echo "<h4>Testing Update Functionality</h4>";
                    $testData = [
                        'exercise_name' => 'Test Update - ' . date('Y-m-d H:i:s'),
                        'updated_by' => 1
                    ];

                    echo "<p><strong>Attempting to update with data:</strong></p>";
                    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

                    $updateResult = $this->exerciseModel->update($id, $testData);
                    echo "<p><strong>Update result:</strong> " . ($updateResult ? 'SUCCESS' : 'FAILED') . "</p>";

                    if (!$updateResult) {
                        $errors = $this->exerciseModel->errors();
                        echo "<p><strong>Validation errors:</strong></p>";
                        echo "<pre>" . json_encode($errors, JSON_PRETTY_PRINT) . "</pre>";
                    } else {
                        // Fetch updated record
                        $updatedExercise = $this->exerciseModel->find($id);
                        echo "<p><strong>Updated exercise data:</strong></p>";
                        echo "<pre>" . json_encode($updatedExercise, JSON_PRETTY_PRINT) . "</pre>";
                    }
                }

                // Test count
                $count = $this->exerciseModel->countAll();
                echo "<p><strong>Total exercises in database:</strong> {$count}</p>";
            }

        } catch (\Exception $e) {
            echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
            echo "<p><strong>Trace:</strong></p><pre>" . $e->getTraceAsString() . "</pre>";
        }
    }

    /**
     * Test update method with simple form
     */
    public function testUpdate($id = 1)
    {
        if ($this->request->getMethod() === 'post') {
            echo "<h3>Test Update - POST Request Received</h3>";
            echo "<p><strong>Exercise ID:</strong> {$id}</p>";
            echo "<p><strong>POST Data:</strong></p>";
            echo "<pre>" . json_encode($this->request->getPost(), JSON_PRETTY_PRINT) . "</pre>";

            try {
                $exercise = $this->exerciseModel->find($id);
                if ($exercise) {
                    $updateData = [
                        'exercise_name' => $this->request->getPost('exercise_name'),
                        'updated_by' => 1
                    ];

                    echo "<p><strong>Attempting update with:</strong></p>";
                    echo "<pre>" . json_encode($updateData, JSON_PRETTY_PRINT) . "</pre>";

                    $result = $this->exerciseModel->update($id, $updateData);
                    echo "<p><strong>Update Result:</strong> " . ($result ? 'SUCCESS' : 'FAILED') . "</p>";

                    if (!$result) {
                        $errors = $this->exerciseModel->errors();
                        echo "<p><strong>Errors:</strong></p>";
                        echo "<pre>" . json_encode($errors, JSON_PRETTY_PRINT) . "</pre>";
                    } else {
                        $updated = $this->exerciseModel->find($id);
                        echo "<p><strong>Updated Record:</strong></p>";
                        echo "<pre>" . json_encode($updated, JSON_PRETTY_PRINT) . "</pre>";
                    }
                } else {
                    echo "<p><strong>Error:</strong> Exercise not found</p>";
                }
            } catch (\Exception $e) {
                echo "<p><strong>Exception:</strong> " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }

            return;
        }

        // GET request - show form
        $exercise = $this->exerciseModel->find($id);
        if (!$exercise) {
            echo "<p>Exercise not found</p>";
            return;
        }

        echo "<h3>Test Update Form</h3>";
        echo "<form method='post'>";
        echo "<p><label>Exercise Name:</label><br>";
        echo "<input type='text' name='exercise_name' value='" . esc($exercise['exercise_name']) . "' style='width: 300px;'></p>";
        echo "<p><input type='submit' value='Update Exercise'></p>";
        echo "</form>";

        echo "<h4>Current Exercise Data:</h4>";
        echo "<pre>" . json_encode($exercise, JSON_PRETTY_PRINT) . "</pre>";
    }

    public function exercise_management()
    {
        return view('exercises/exercises_exercise_management', [
            'title' => 'Exercise Management',
            'menu' => 'exercises'
        ]);
    }

    public function list()
    {
        try {
            // Get exercises from database
            $orgId = session()->get('org_id');
            if ($orgId) {
                $exercises = $this->exerciseModel->getExercisesByOrgId($orgId);
            } else {
                $exercises = $this->exerciseModel->findAll();
            }

            return $this->response->setJSON(['data' => $exercises]);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercises: ' . $e->getMessage());
            return $this->response->setJSON(['data' => []]);
        }
    }

    public function createForm()
    {
        return view('exercises/exercises_create', [
            'title' => 'Create Exercise',
            'menu' => 'exercises'
        ]);
    }

    public function create()
    {
        if ($this->request->getMethod() !== 'post') {
            return redirect()->to(base_url('exercises/create'));
        }

        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id') ?? 1;
        $data['status'] = 'draft';

        // Set org_id from session if not provided
        if (empty($data['org_id'])) {
            $data['org_id'] = session()->get('org_id') ?? 1;
        }

        try {
            $insertId = $this->exerciseModel->insert($data);

            if ($insertId) {
                session()->setFlashdata('success', 'Exercise created successfully');
                return redirect()->to(base_url('exercises'));
            } else {
                // Get validation errors
                $errors = $this->exerciseModel->errors();
                if (!empty($errors)) {
                    $errorMessage = implode(', ', $errors);
                    session()->setFlashdata('error', 'Validation failed: ' . $errorMessage);
                } else {
                    session()->setFlashdata('error', 'Failed to create exercise');
                }
                return redirect()->back()->withInput();
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating exercise: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error creating exercise: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function view($id = null)
    {
        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        try {
            $exercise = $this->exerciseModel->find($id);

            if (!$exercise) {
                session()->setFlashdata('error', 'Exercise not found');
                return redirect()->to(base_url('exercises'));
            }

            return view('exercises/exercises_view', [
                'title' => 'View Exercise',
                'menu' => 'exercises',
                'exercise' => $exercise
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercise: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading exercise details');
            return redirect()->to(base_url('exercises'));
        }
    }

    public function editForm($id = null)
    {
        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        try {
            $exercise = $this->exerciseModel->find($id);

            if (!$exercise) {
                session()->setFlashdata('error', 'Exercise not found');
                return redirect()->to(base_url('exercises'));
            }

            return view('exercises/exercises_edit', [
                'title' => 'Edit Exercise',
                'menu' => 'exercises',
                'exercise' => $exercise
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercise for edit: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading exercise for editing');
            return redirect()->to(base_url('exercises'));
        }
    }

    public function get($id = null)
    {
        // Keep this method for any AJAX calls that might still need it
        try {
            $exercise = $this->exerciseModel->find($id);

            if (!$exercise) {
                return $this->response->setJSON([
                    'error' => 'Exercise not found'
                ]);
            }

            return $this->response->setJSON($exercise);
        } catch (\Exception $e) {
            log_message('error', 'Error fetching exercise via AJAX: ' . $e->getMessage());
            return $this->response->setJSON([
                'error' => 'Error loading exercise data'
            ]);
        }
    }

    public function update($id = null)
    {
        // Add debug logging at the very start
        log_message('debug', '=== UPDATE METHOD CALLED ===');
        log_message('debug', 'Request method: ' . $this->request->getMethod());
        log_message('debug', 'Exercise ID: ' . $id);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        if ($this->request->getMethod() !== 'post') {
            log_message('debug', 'Not a POST request, redirecting to edit form');
            return redirect()->to(base_url('exercises/edit/' . $id));
        }

        if (!$id) {
            log_message('debug', 'No ID provided, redirecting to exercises list');
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        try {
            // Check if exercise exists
            $existingExercise = $this->exerciseModel->find($id);
            if (!$existingExercise) {
                session()->setFlashdata('error', 'Exercise not found');
                return redirect()->to(base_url('exercises'));
            }

            // Get form data
            $data = $this->request->getPost();
            unset($data['id']); // Remove ID from data to prevent issues

            // Add updated_by field
            $data['updated_by'] = session()->get('user_id') ?? 1;

            // Debug logging
            log_message('debug', 'Update attempt for exercise ID: ' . $id);
            log_message('debug', 'Form data received: ' . json_encode($data));
            log_message('debug', 'Existing exercise data: ' . json_encode($existingExercise));

            // Clean the data - remove empty values to avoid validation issues
            $cleanData = [];
            foreach ($data as $key => $value) {
                if ($value !== '' && $value !== null) {
                    $cleanData[$key] = $value;
                }
            }

            log_message('debug', 'Cleaned data for update: ' . json_encode($cleanData));

            // Update the exercise with cleaned data
            $updateSuccess = $this->exerciseModel->update($id, $cleanData);

            log_message('debug', 'Update result: ' . ($updateSuccess ? 'SUCCESS' : 'FAILED'));

            if ($updateSuccess) {
                // Verify the update by fetching the record again
                $updatedExercise = $this->exerciseModel->find($id);
                log_message('debug', 'Updated exercise data: ' . json_encode($updatedExercise));

                session()->setFlashdata('success', 'Exercise updated successfully');
                return redirect()->to(base_url('exercises'));
            } else {
                // Get validation errors
                $errors = $this->exerciseModel->errors();
                log_message('error', 'Update failed. Validation errors: ' . json_encode($errors));

                if (!empty($errors)) {
                    $errorMessage = implode(', ', $errors);
                    session()->setFlashdata('error', 'Validation failed: ' . $errorMessage);
                } else {
                    session()->setFlashdata('error', 'Failed to update exercise - no specific error returned');
                }
                return redirect()->back()->withInput();
            }
        } catch (\Exception $e) {
            log_message('error', 'Exception during exercise update: ' . $e->getMessage());
            log_message('error', 'Exception trace: ' . $e->getTraceAsString());
            session()->setFlashdata('error', 'Error updating exercise: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function delete($id = null)
    {
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to(base_url('exercises'));
        }

        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        try {
            // Get exercise data for validation
            $exercise = $this->exerciseModel->find($id);

            if (!$exercise) {
                session()->setFlashdata('error', 'Exercise not found');
                return redirect()->to(base_url('exercises'));
            }

            // Only allow deletion if exercise is in draft status
            if ($exercise['status'] !== 'draft') {
                session()->setFlashdata('error', 'Only exercises in draft status can be deleted');
                return redirect()->to(base_url('exercises'));
            }

            // Delete the exercise (soft delete)
            $deleteSuccess = $this->exerciseModel->delete($id);

            if ($deleteSuccess) {
                session()->setFlashdata('success', 'Exercise deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete exercise');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting exercise: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error deleting exercise: ' . $e->getMessage());
        }

        return redirect()->to(base_url('exercises'));
    }

    /**
     * Change the status of an exercise
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function changeStatus()
    {
        // Get and validate the exercise ID
        $id = $this->request->getPost('id');
        $status = $this->request->getPost('status');

        // Basic validation
        if (empty($id) || !is_numeric($id)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid exercise ID'
            ]);
        }

        // Force ID to be integer
        $id = (int)$id;

        // Debug logging
        log_message('debug', 'Exercise status change request: ID=' . $id . ', Status=' . $status);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Validate the status - Updated to match database ENUM values
        $validStatuses = ['draft', 'published', 'selection', 'archived'];
        if (!in_array($status, $validStatuses)) {
            log_message('debug', 'Invalid status: ' . $status);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid status value'
            ]);
        }

        try {
            // Get exercise data from database
            $exercise = $this->exerciseModel->find($id);
            log_message('debug', 'Exercise lookup result: ' . ($exercise ? 'Found' : 'Not found'));

            if (!$exercise) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found with ID: ' . $id
                ]);
            }

            // Update the exercise status
            $data = [
                'status' => $status,
                'updated_by' => session()->get('user_id') ?? 1
            ];

            log_message('debug', 'Attempting to update exercise with data: ' . json_encode($data));

            $updateSuccess = $this->exerciseModel->update($id, $data);

            if ($updateSuccess) {
                log_message('debug', 'Exercise status update successful for ID: ' . $id);
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status))
                ]);
            }

            log_message('debug', 'Exercise status update failed for ID: ' . $id);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update exercise status'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception updating exercise: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating exercise: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the pre-screening criteria management page for an exercise
     *
     * @param int $id Exercise ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function pre_screen_criteria($id = null)
    {
        // Check if ID is provided
        if (!$id) {
            session()->setFlashdata('error', 'No exercise selected.');
            return redirect()->to(base_url('exercises'));
        }

        try {
            // Check if exercise exists
            $exercise = $this->exerciseModel->find($id);
            if (!$exercise) {
                session()->setFlashdata('error', 'Exercise not found.');
                return redirect()->to(base_url('exercises'));
            }

            return view('exercises/exercises_pre_screen_criteria', [
                'title' => 'Pre-Screening Criteria',
                'menu' => 'exercises',
                'exercise_id' => $id
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error loading pre-screening criteria page: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading pre-screening criteria page');
            return redirect()->to(base_url('exercises'));
        }
    }

    /**
     * Save pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function save_criteria($id = null)
    {
        try {
            // Check if exercise exists
            $exercise = $this->exerciseModel->find($id);
            if (!$exercise) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found',
                    'csrf_hash' => csrf_hash()
                ]);
            }

            // Get pre-screening criteria from request
            $criteria = $this->request->getPost('pre_screen_criteria');

            // Validate JSON format
            if ($criteria) {
                try {
                    // Decode and re-encode to ensure valid JSON
                    json_decode($criteria);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        return $this->response->setJSON([
                            'success' => false,
                            'message' => 'Invalid criteria format: ' . json_last_error_msg(),
                            'csrf_hash' => csrf_hash()
                        ]);
                    }
                } catch (\Exception $e) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Invalid criteria format: ' . $e->getMessage(),
                        'csrf_hash' => csrf_hash()
                    ]);
                }
            }

            // Update exercise with criteria
            $updateData = [
                'pre_screen_criteria' => $criteria,
                'updated_by' => session()->get('user_id') ?? 1
            ];

            $updateSuccess = $this->exerciseModel->update($id, $updateData);

            if ($updateSuccess) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Pre-screening criteria saved successfully',
                    'csrf_hash' => csrf_hash()
                ]);
            }

            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to save pre-screening criteria',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error saving criteria: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error saving criteria: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Get pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function get_criteria($id = null)
    {
        try {
            // Get exercise data from database
            $exercise = $this->exerciseModel->find($id);

            if (!$exercise) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Exercise not found'
                ]);
            }

            // Get pre-screening criteria
            $criteria = [];
            if (!empty($exercise['pre_screen_criteria'])) {
                try {
                    $criteria = json_decode($exercise['pre_screen_criteria'], true);
                } catch (\Exception $e) {
                    log_message('error', 'Error decoding criteria JSON: ' . $e->getMessage());
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'criteria' => $criteria
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Error getting criteria: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error loading criteria'
            ]);
        }
    }
}