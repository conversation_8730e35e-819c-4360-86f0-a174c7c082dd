<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class ExercisesController extends ResourceController
{
    protected $helpers = ['form', 'url', 'info'];

    public function __construct()
    {
        // Remove model initialization for UI development
    }

    public function exercise_management()
    {
        return view('exercises/exercises_exercise_management', [
            'title' => 'Exercise Management',
            'menu' => 'exercises'
        ]);
    }

    public function list()
    {
        // Dummy data for UI development - replace with actual model calls later
        $exercises = [
            [
                'id' => 1,
                'org_id' => 1,
                'exercise_name' => 'IT Recruitment Exercise 2024',
                'gazzetted_no' => 'GAZ-2024-001',
                'gazzetted_date' => '2024-01-15',
                'advertisement_no' => 'ADV-2024-001',
                'advertisement_date' => '2024-01-20',
                'mode_of_advertisement' => 'Online',
                'publish_date_from' => '2024-01-25',
                'publish_date_to' => '2024-12-31',
                'description' => 'Recruitment exercise for IT positions',
                'status' => 'draft',
                'created_at' => '2024-01-10 09:00:00'
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'exercise_name' => 'Finance Department Recruitment',
                'gazzetted_no' => 'GAZ-2024-002',
                'gazzetted_date' => '2024-01-16',
                'advertisement_no' => 'ADV-2024-002',
                'advertisement_date' => '2024-01-21',
                'mode_of_advertisement' => 'Print & Online',
                'publish_date_from' => '2024-01-26',
                'publish_date_to' => '2024-11-30',
                'description' => 'Recruitment exercise for finance positions',
                'status' => 'publish',
                'created_at' => '2024-01-09 11:15:00'
            ]
        ];
        return $this->response->setJSON(['data' => $exercises]);
    }

    public function createForm()
    {
        return view('exercises/exercises_create', [
            'title' => 'Create Exercise',
            'menu' => 'exercises'
        ]);
    }

    public function create()
    {
        if ($this->request->getMethod() !== 'post') {
            return redirect()->to(base_url('exercises/create'));
        }

        $data = $this->request->getPost();
        $data['created_by'] = session()->get('user_id') ?? 1;
        $data['status'] = 'draft';

        // Simulate successful creation for UI development
        $insertSuccess = true; // Replace with actual model insert call later

        if ($insertSuccess) {
            session()->setFlashdata('success', 'Exercise created successfully');
            return redirect()->to(base_url('exercises'));
        }

        session()->setFlashdata('error', 'Failed to create exercise');
        return redirect()->back()->withInput();
    }

    public function view($id = null)
    {
        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        // Dummy exercise data for UI development
        $exercise = [
            'id' => $id,
            'org_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'gazzetted_no' => 'GAZ-2024-001',
            'gazzetted_date' => '2024-01-15',
            'advertisement_no' => 'ADV-2024-001',
            'advertisement_date' => '2024-01-20',
            'mode_of_advertisement' => 'Online',
            'publish_date_from' => '2024-01-25',
            'publish_date_to' => '2024-12-31',
            'description' => 'Recruitment exercise for IT positions',
            'status' => 'draft',
            'created_at' => '2024-01-10 09:00:00'
        ];

        return view('exercises/exercises_view', [
            'title' => 'View Exercise',
            'menu' => 'exercises',
            'exercise' => $exercise
        ]);
    }

    public function editForm($id = null)
    {
        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        // Dummy exercise data for UI development
        $exercise = [
            'id' => $id,
            'org_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'gazzetted_no' => 'GAZ-2024-001',
            'gazzetted_date' => '2024-01-15',
            'advertisement_no' => 'ADV-2024-001',
            'advertisement_date' => '2024-01-20',
            'mode_of_advertisement' => 'Online',
            'publish_date_from' => '2024-01-25',
            'publish_date_to' => '2024-12-31',
            'description' => 'Recruitment exercise for IT positions',
            'status' => 'draft',
            'created_at' => '2024-01-10 09:00:00'
        ];

        return view('exercises/exercises_edit', [
            'title' => 'Edit Exercise',
            'menu' => 'exercises',
            'exercise' => $exercise
        ]);
    }

    public function get($id = null)
    {
        // Keep this method for any AJAX calls that might still need it
        // Dummy exercise data for UI development
        $exercise = [
            'id' => $id,
            'org_id' => 1,
            'exercise_name' => 'IT Recruitment Exercise 2024',
            'gazzetted_no' => 'GAZ-2024-001',
            'gazzetted_date' => '2024-01-15',
            'advertisement_no' => 'ADV-2024-001',
            'advertisement_date' => '2024-01-20',
            'mode_of_advertisement' => 'Online',
            'publish_date_from' => '2024-01-25',
            'publish_date_to' => '2024-12-31',
            'description' => 'Recruitment exercise for IT positions',
            'status' => 'draft',
            'created_at' => '2024-01-10 09:00:00'
        ];
        return $this->response->setJSON($exercise);
    }

    public function update($id = null)
    {
        if ($this->request->getMethod() !== 'post') {
            return redirect()->to(base_url('exercises/edit/' . $id));
        }

        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        $data = $this->request->getPost();
        unset($data['id']);

        $data['updated_by'] = session()->get('user_id') ?? 1;

        // Simulate successful update for UI development
        $updateSuccess = true; // Replace with actual model update call later

        if ($updateSuccess) {
            session()->setFlashdata('success', 'Exercise updated successfully');
            return redirect()->to(base_url('exercises'));
        }

        session()->setFlashdata('error', 'Failed to update exercise');
        return redirect()->back()->withInput();
    }

    public function delete($id = null)
    {
        if ($this->request->getMethod() !== 'post') {
            session()->setFlashdata('error', 'Invalid request method');
            return redirect()->to(base_url('exercises'));
        }

        if (!$id) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        // Dummy exercise data for validation
        $exercise = [
            'id' => $id,
            'status' => 'draft' // Simulate draft status for UI development
        ];

        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('exercises'));
        }

        // Only allow deletion if exercise is in draft status
        if ($exercise['status'] !== 'draft') {
            session()->setFlashdata('error', 'Only exercises in draft status can be deleted');
            return redirect()->to(base_url('exercises'));
        }

        // Simulate successful deletion for UI development
        $deleteSuccess = true; // Replace with actual model delete call later

        if ($deleteSuccess) {
            session()->setFlashdata('success', 'Exercise deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete exercise');
        }

        return redirect()->to(base_url('exercises'));
    }

    /**
     * Change the status of an exercise
     *
     * @return \CodeIgniter\HTTP\Response
     */
    public function changeStatus()
    {
        // Get and validate the exercise ID
        $id = $this->request->getPost('id');
        $status = $this->request->getPost('status');

        // Basic validation
        if (empty($id) || !is_numeric($id)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid exercise ID'
            ]);
        }

        // Force ID to be integer
        $id = (int)$id;

        // Debug logging
        log_message('debug', 'Exercise status change request: ID=' . $id . ', Status=' . $status);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Validate the status
        $validStatuses = ['draft', 'publish', 'publish_request', 'selection', 'review', 'closed'];
        if (!in_array($status, $validStatuses)) {
            log_message('debug', 'Invalid status: ' . $status);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid status value'
            ]);
        }

        // Dummy exercise data for UI development
        $exercise = [
            'id' => $id,
            'status' => 'draft' // Simulate existing exercise
        ];
        log_message('debug', 'Exercise lookup result: Found (simulated)');

        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found with ID: ' . $id
            ]);
        }

        // Update the exercise status (simulated)
        $data = [
            'status' => $status,
            'updated_by' => session()->get('user_id') ?? 1 // Fallback to 1 if not set
        ];

        log_message('debug', 'Attempting to update exercise with data: ' . json_encode($data));

        try {
            // Simulate successful update for UI development
            $updateSuccess = true; // Replace with actual model update call later

            if ($updateSuccess) {
                log_message('debug', 'Exercise status update successful for ID: ' . $id);
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status))
                ]);
            }

            log_message('debug', 'Exercise status update failed for ID: ' . $id);
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update exercise status'
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Exception updating exercise: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating exercise: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the pre-screening criteria management page for an exercise
     *
     * @param int $id Exercise ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function pre_screen_criteria($id = null)
    {
        // Check if ID is provided
        if (!$id) {
            session()->setFlashdata('error', 'No exercise selected.');
            return redirect()->to(base_url('exercises'));
        }

        // Dummy exercise validation for UI development
        $exercise = ['id' => $id]; // Simulate exercise exists
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('exercises'));
        }

        return view('exercises/exercises_pre_screen_criteria', [
            'title' => 'Pre-Screening Criteria',
            'menu' => 'exercises',
            'exercise_id' => $id
        ]);
    }

    /**
     * Save pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function save_criteria($id = null)
    {
        // Dummy exercise validation for UI development
        $exercise = ['id' => $id]; // Simulate exercise exists
        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found',
                'csrf_hash' => csrf_hash()
            ]);
        }

        // Get pre-screening criteria from request
        $criteria = $this->request->getPost('pre_screen_criteria');

        // Validate JSON format
        if ($criteria) {
            try {
                // Decode and re-encode to ensure valid JSON
                json_decode($criteria);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Invalid criteria format: ' . json_last_error_msg(),
                        'csrf_hash' => csrf_hash()
                    ]);
                }
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Invalid criteria format: ' . $e->getMessage(),
                    'csrf_hash' => csrf_hash()
                ]);
            }
        }

        // Simulate successful update for UI development
        $updateData = [
            'pre_screen_criteria' => $criteria,
            'updated_by' => session()->get('user_id') ?? 1
        ];

        $updateSuccess = true; // Replace with actual model update call later

        if ($updateSuccess) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Pre-screening criteria saved successfully',
                'csrf_hash' => csrf_hash()
            ]);
        }

        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to save pre-screening criteria',
            'csrf_hash' => csrf_hash()
        ]);
    }

    /**
     * Get pre-screening criteria for an exercise
     *
     * @param int $id Exercise ID
     * @return \CodeIgniter\HTTP\Response
     */
    public function get_criteria($id = null)
    {
        // Dummy exercise data for UI development
        $exercise = [
            'id' => $id,
            'pre_screen_criteria' => json_encode([
                ['criteria' => 'Bachelor degree in relevant field', 'required' => true],
                ['criteria' => 'Minimum 2 years experience', 'required' => true],
                ['criteria' => 'Professional certification preferred', 'required' => false]
            ])
        ];

        if (!$exercise) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Exercise not found'
            ]);
        }

        // Get pre-screening criteria
        $criteria = [];
        if (!empty($exercise['pre_screen_criteria'])) {
            try {
                $criteria = json_decode($exercise['pre_screen_criteria'], true);
            } catch (\Exception $e) {
                log_message('error', 'Error decoding criteria JSON: ' . $e->getMessage());
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'criteria' => $criteria
        ]);
    }
}