<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Exercise Management</h5>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    <a href="<?= base_url('exercises/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>New Exercise
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table id="exerciseTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Exercise Name</th>
                            <th>Gazzetted No</th>
                            <th>Advertisement No</th>
                            <th>Publish Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>



<!-- Add the status change modal at the end of the page -->
<div class="modal fade" id="changeStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt me-2"></i> Change Exercise Status
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="changeStatusForm" action="<?= base_url('exercises/change-status') ?>" method="post">
                <!-- CSRF token -->
                <?= csrf_field() ?>

                <div class="modal-body p-4">
                    <div class="text-center mb-4">
                        <div class="d-inline-block p-3 rounded-circle bg-light mb-3">
                            <i class="fas fa-clipboard-list text-primary" style="font-size: 2rem;"></i>
                        </div>
                        <h5 id="exercise_name"></h5>
                        <p class="text-muted">Current Status: <span class="badge bg-secondary" id="current_status">Draft</span></p>
                    </div>

                    <input type="hidden" id="status_exercise_id" name="id">

                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-select form-select-lg" id="new_status" name="status" required>
                            <option value="draft">Draft</option>
                            <option value="publish_request">Publish Request</option>
                            <option value="publish">Publish</option>
                            <option value="selection">Selection</option>
                            <option value="review">Review</option>
                            <option value="closed">Closed</option>
                        </select>
                        <div class="form-text text-muted mt-2">
                            <ul class="ps-3 mb-0">
                                <li><b>Draft</b>: Work in progress, not visible to public</li>
                                <li><b>Publish Request</b>: Pending approval for publication</li>
                                <li><b>Publish</b>: Publicly visible exercise</li>
                                <li><b>Selection</b>: In selection phase</li>
                                <li><b>Review</b>: Under review</li>
                                <li><b>Closed</b>: Exercise is closed and archived</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Your custom script -->
<script>
// Debug helper function - only logs in development environment
function debug(label, data) {
    // Uncomment the line below if you need debugging
    // console.log(`DEBUG [${label}]:`, data);
}

document.addEventListener('DOMContentLoaded', function() {
    if (typeof jQuery != 'undefined') {
        initializeExerciseManagement();
    } else {
        console.error('jQuery is not loaded');
    }
});

function initializeExerciseManagement() {
    // Initialize DataTable
    const table = $('#exerciseTable').DataTable({
        ajax: {
            url: '<?= base_url('exercises/list') ?>',
            dataSrc: function(json) {
                // Debug call removed
                return json.data;
            }
        },
        columns: [
            { data: 'exercise_name' },
            { data: 'gazzetted_no' },
            { data: 'advertisement_no' },
            { data: 'publish_date_from' },
            {
                data: 'status',
                render: function(data) {
                    // Updated status names and colors to match Dakoii implementation
                    const statusClasses = {
                        'draft': 'bg-secondary',
                        'publish': 'bg-success',
                        'publish_request': 'bg-warning',
                        'selection': 'bg-info',
                        'review': 'bg-primary',
                        'closed': 'bg-danger'
                    };

                    // Determine text color based on background
                    const textColor = (data === 'publish_request' || data === 'warning') ? 'text-dark' : 'text-white';

                    // Format the status display with proper capitalization and spacing
                    const displayStatus = data.replace('_', ' ');

                    // Improved contrast with solid colors and proper text color
                    return `<span class="badge ${statusClasses[data] || 'bg-secondary'} ${textColor}" style="font-weight: 500; padding: 0.35em 0.65em;">${displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}</span>`;
                }
            },
            {
                data: null,
                render: function(data) {
                    const viewButton = `
                        <a href="<?= base_url('exercises/view') ?>/${data.id}" class="btn btn-sm btn-info" title="View Details">
                            <i class="fas fa-eye"></i>
                        </a>`;

                    const editButton = `
                        <a href="<?= base_url('exercises/edit') ?>/${data.id}" class="btn btn-sm btn-primary" title="Edit Exercise">
                            <i class="fas fa-edit"></i>
                        </a>`;

                    // Only show delete button for exercises in draft status
                    let deleteButton = '';
                    if (data.status === 'draft') {
                        deleteButton = `
                        <button class="btn btn-sm btn-danger delete-btn" data-id="${data.id}" title="Delete Exercise">
                            <i class="fas fa-trash"></i>
                        </button>`;
                    }

                    // Add status change button for all statuses
                    const statusButton = `
                    <button class="btn btn-sm btn-success change-status-btn" data-id="${data.id}" data-name="${data.exercise_name}" data-status="${data.status}" title="Change Status">
                        <i class="fas fa-exchange-alt"></i>
                    </button>`;

                    // Add pre-screening criteria button
                    const criteriaButton = `
                    <a href="<?= base_url('exercises/pre_screen_criteria') ?>/${data.id}" class="btn btn-sm btn-warning" title="Pre-Screening Criteria">
                        <i class="fas fa-filter"></i>
                    </a>`;

                    return `<div class="btn-group">${viewButton}${editButton}${statusButton}${criteriaButton}${deleteButton}</div>`;
                }
            }
        ]
    });



    // Delete Button Handler
    $('#exerciseTable').on('click', '.delete-btn', function() {
        const id = $(this).data('id');
        const row = $(this).closest('tr');
        const rowData = table.row(row).data();

        // Double-check that status is draft before allowing deletion
        if (rowData.status !== 'draft') {
            toastr.error('Only exercises in draft status can be deleted');
            return;
        }

        if (confirm('Are you sure you want to delete this exercise? This action cannot be undone.')) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?= base_url('exercises/delete') ?>/${id}`;

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '<?= csrf_token() ?>';
            csrfInput.value = '<?= csrf_hash() ?>';
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        }
    });

    // Reset status change modal when closed
    $('#changeStatusModal').on('hidden.bs.modal', function() {
        $('#changeStatusForm')[0].reset();
        $('#status_exercise_id').val('');
        $('#exercise_name').text('');
        $('#current_status').removeClass().addClass('badge bg-secondary').text('Draft');
    });

    // Add event handler for status change button
    $('#exerciseTable').on('click', '.change-status-btn', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        const currentStatus = $(this).data('status');

        // Verify that we have a valid ID
        if (!id) {
            toastr.error('Cannot change status: Invalid exercise ID');
            return;
        }

        // Open status change modal
        $('#changeStatusModal').modal('show');

        // Set the ID
        $('#status_exercise_id').val(id);
        $('#exercise_name').text(name);

        // Set appropriate badge class and format text
        const statusClasses = {
            'draft': 'bg-secondary',
            'publish': 'bg-success',
            'publish_request': 'bg-warning',
            'selection': 'bg-info',
            'review': 'bg-primary',
            'closed': 'bg-danger'
        };

        const textColor = (currentStatus === 'publish_request') ? 'text-dark' : 'text-white';
        const displayStatus = currentStatus.replace('_', ' ');
        const formattedStatus = displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1);

        $('#current_status')
            .removeClass()
            .addClass(`badge ${statusClasses[currentStatus] || 'bg-secondary'} ${textColor}`)
            .text(formattedStatus);

        // Set current status in dropdown
        $('#new_status').val(currentStatus);
    });

    // Initialize status change form submission
    $('#changeStatusForm').on('submit', function(e) {
        e.preventDefault();

        // Check if ID is present
        const exerciseId = $('#status_exercise_id').val();
        const newStatus = $('#new_status').val();

        if (!exerciseId || exerciseId === '') {
            toastr.error('Cannot update status: Missing exercise ID');
            return;
        }

        $.ajax({
            type: 'POST',
            url: $(this).attr('action'),
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success notification
                    toastr.success(response.message);

                    // Close the modal
                    $('#changeStatusModal').modal('hide');

                    // Refresh the table
                    $('#exerciseTable').DataTable().ajax.reload();
                } else {
                    // Show error notification
                    toastr.error(response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Status change error:', error);
                toastr.error('An error occurred while updating the status.');
            }
        });
    });
}
</script>
<?= $this->endSection() ?>